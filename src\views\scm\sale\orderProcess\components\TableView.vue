<template>
    <!-- 表格工具栏 -->
    <div class="table-header">
      <div class="header-content">
        <div class="header-title">
          <h3 class="title-text">销售订单履约跟踪</h3>
          <div class="title-subtitle">实时监控销售订单从创建到交付的全过程</div>
        </div>
        <div class="header-search">
          <!-- 搜索表单 - 单行布局 -->
          <div class="search-form-container">
            <div class="search-single-row">
              <el-input
                v-model="localQueryParams.orderNo"
                placeholder="搜索订单编号"
                clearable
                class="search-input"
                @input="handleParamsChange"
                @keyup.enter="handleQuery"
              />
              <el-input
                v-model="localQueryParams.customerName"
                placeholder="搜索客户名称"
                clearable
                class="search-input"
                @input="handleParamsChange"
                @keyup.enter="handleQuery"
              />
              <el-input
                v-model="localQueryParams.productName"
                placeholder="搜索产品名称"
                clearable
                class="search-input"
                @input="handleParamsChange"
                @keyup.enter="handleQuery"
              />
              <el-input
                v-model="localQueryParams.productCode"
                placeholder="搜索产品编码"
                clearable
                class="search-input"
                @input="handleParamsChange"
                @keyup.enter="handleQuery"
              />
              <el-select
                v-model="localQueryParams.approvalStatus"
                placeholder="审批状态"
                clearable
                class="search-select"
                @change="handleParamsChange"
              >
                <el-option
                  v-for="dict in getStrDictOptions(DICT_TYPE.APPROVE_STATUS)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
              <el-button @click="handleQuery" type="primary" class="form-btn">
                <Icon icon="ep:search" class="btn-icon" />
                搜索
              </el-button>
              <el-button @click="refreshData" type="info" class="form-btn">
                <Icon icon="ep:refresh" class="btn-icon" />
                刷新
              </el-button>
              <!-- 更多操作下拉菜单 -->
              <el-dropdown @command="handleDropdownCommand" class="more-actions-dropdown">
                <el-button class="form-btn">
                  更多操作
                  <Icon icon="ep:arrow-down" class="btn-icon" />
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="export">
                      <Icon icon="ep:download" class="dropdown-icon" />
                      导出数据
                    </el-dropdown-item>
                    <el-dropdown-item command="init">
                      <Icon icon="ep:upload" class="dropdown-icon" />
                      初始化
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 表格主体 -->
<!--    <div class="table-container">-->
    <el-table
      v-loading="props.loading"
      :data="displayData"
      border
      stripe
      :show-overflow-tooltip="true"
      highlight-current-row
      @sort-change="handleSortChange"
      @selection-change="handleSelectionChange"
      :row-class-name="getRowClassName"
      :header-cell-style="getHeaderCellStyle"
      :cell-style="getCellStyle"
      :max-height="600"
    >
      <!-- 选择列 -->
        <el-table-column type="selection" width="50" align="center" />

        <!-- 订单信息 -->
      <el-table-column
          label="订单信息"
          width="200"
        fixed="left"
        sortable="custom"
          prop="orderNo"
      >
        <template #default="{ row }">
            <div class="order-cell">
              <div class="order-no">
                <el-link type="primary" @click="handleOrderDetail(row)" class="order-link">
                  {{ row.salesOrderInfo.orderNo }}
            </el-link>
              </div>
              <div class="customer-name">{{ row.salesOrderInfo.customer.name || '-' }}</div>
              <div class="customer-name">下单时间：{{ formatToDate(row.salesOrderInfo.orderDate) || '-' }}</div>
              <div class="customer-name">发货时间：{{ formatToDate(row.salesOrderInfo.deliveryTime) || '-' }}</div>
              <div class="customer-name">要求: {{ row.salesOrderInfo.requirement || row.salesOrderInfo.remark || '-' }}</div>
            </div>
        </template>
      </el-table-column>

      <!-- 产品信息 -->
      <el-table-column
        label="产品信息"
        show-overflow-tooltip
        width="160"
      >
        <template #default="{ row }">
            <div class="product-cell">
              <div class="product-name">{{ row.salesOrderInfo.product.name }}</div>
              <div class="product-code">{{ row.salesOrderInfo.product.fullCode }}</div>
              <div class="product-info">{{ row.salesOrderInfo.product.spec }}</div>
              <div class="product-info">
                {{ row.salesOrderInfo.product.quantity + "" + row.salesOrderInfo.product.unitName
                    + " " + row.salesOrderInfo.product?.specQuantity}}
                <el-tag type="info" v-if="row.salesOrderInfo.product?.specQuantity">{{ row.salesOrderInfo.product?.specQuantity }}</el-tag>
              </div>
          </div>
        </template>
      </el-table-column>

        <!-- 数量 -->
      <el-table-column
        label="数量"
          width="100"
          align="center"
          sortable="custom"
      >
        <template #default="{ row }">
            <div class="quantity-cell">
              <div class="main-quantity">{{ row.salesOrderInfo.product?.quantity || 0 }} {{row.salesOrderInfo.product?.unitName}}</div>
              <div>{{row.salesOrderInfo.product?.specQuantity}}</div>
              <div class="completed-quantity">已完成：{{ row.outbound?.quantity || 0 }} {{ row.salesOrderInfo?.product?.unitName }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        label="需求状态" align="center" width='180'>
        <template #default="{ row }">
            <div class="stock-cell" v-if="row.request">
              <div><dict-tag :type="DICT_TYPE.MFG_REQUEST_STATUS" :value="row.request.status" /></div>
              <div>创建时间：{{ formatToDate(row.request.createTime) }}</div>
              <div v-if="row.request.status != '0'">BOM确认时间: {{ formatToDate(row.request.confirmTime) }}</div>
            </div>
        </template>
      </el-table-column>
      <!-- 库存状态 -->
      <el-table-column
          label="原料库存"
          align="center"
          width="140"
          class-name="hidden-on-mobile"
      >
        <template #default="{ row }">
            <div class="stock-cell">
              <div class="stock-status-tag">
                <dict-tag :type="DICT_TYPE.COMMON_TASK_STATUS" :value="row.materialInventory?.status" />
              </div>
              <div class="stock-progress">
                <div class="progress-bar">
                  <div
                    class="progress-fill"
                    :class="getStockProgressColor(row)"
                    :style="{ width: getStockProgress(row) + '%' }" ></div>
                </div>
                <span class="progress-text">{{ getStockProgress(row) }}%</span>
              </div>
              <div class="process-divider"></div>   
              <el-tooltip placement="top" v-if="row.materialInventory?.rawMaterials">
                <template #content>
                  <div v-for="item in row.materialInventory?.rawMaterials" :key="item.materialId" class="material-item-row">
                    <span class="material-info">
                      {{ item.fullCode + " " + item.name }}：{{ item.pendingQty }} {{ item.unitName }}
                    </span>
                    <el-tag :type="item.shortage?'danger':'success'" class="material-status">
                      {{ item.shortage?('缺料' + item.shortageQty):'已备齐'}}
                    </el-tag>
                  </div>
                </template>
                <div class="stock-summary">
                  <Icon icon="ep:view" class="view-icon" />
                </div>
              </el-tooltip>
<!--              <div class="stock-info" v-for="item in row.materialInventory?.rawMaterials" :key="item.materialId">
                {{ item.fullCode + " " + item.name }}：{{ item.pendingQty }} {{ item.unitName }}
                <el-tag :type="item.shortage?'danger':'success'">{{ item.shortage?('缺料' + item.shortageQty):'已备齐'}}</el-tag>
              </div>-->
          </div>
        </template>
      </el-table-column>

      <!-- 采购 -->
      <el-table-column
        label="采购"
        align="center"
        width="150"
      >
        <template #default="{ row }">
          <div class="process-cell">
            <el-tag
              :type="getProcessStatusType(getProcessStatus(row, 'purchase'))"
              size="small"
              class="process-tag"
              :class="getProcessStatusClass(getProcessStatus(row, 'purchase'))"
              effect="plain"
            >
              <Icon :icon="getProcessStatusIcon(getProcessStatus(row, 'purchase'))" class="tag-icon" />
              {{ getProcessStatusLabel(getProcessStatus(row, 'purchase')) }}
            </el-tag>
            <div class="process-info">
              <div class="stock-progress">
                <div class="progress-bar">
                  <div
                    class="progress-fill"
                    :class="getProcessProgress(row.procurement?.progress || 0, 'purchase') >= 80 ? 'progress-green' : getProcessProgress(row.procurement?.progress || 0, 'purchase') >= 60 ? 'progress-yellow' : 'progress-red'"
                    :style="{ width: getProcessProgress(row.procurement?.progress || 0, 'purchase') + '%' }"></div>
                </div>
                <span class="progress-text">{{ getProcessProgress(row.procurement?.progress || 0, 'purchase') }}%</span>
              </div>
              <div class="process-divider"></div>
              <el-tooltip placement="top" v-if="row.procurement?.purchaseItems" effect="dark">
                <template #content>
                  <div v-for="item in row.procurement?.purchaseItems" :key="item.id" class="purchase-item-row">
                    <span class="purchase-info">
                      {{ (item.purchaseTime?formatDate(item.purchaseTime): "") + " " + item.materialCode + " " + item.materialName }}
                    </span>
                    <dict-tag :type="DICT_TYPE.PURCHASE_ORDER_STATUS" :value="item.purchaseStatus" class="purchase-status" />
                  </div>
                  <div class="tooltip-completion-time">
                    完成时间: {{ formatDate(getProcessCompleteDate(row, 'purchase')) }}
                  </div>
                </template>
                <div class="stock-summary">
                  <Icon icon="ep:view" class="view-icon" />
                </div>
              </el-tooltip>
            </div>
          </div>
        </template>
      </el-table-column>

        <!-- 计划 -->
      <el-table-column
          label="计划"
        align="center"
        width="180"
      >
        <template #default="{ row }">
            <div class="process-cell">
          <!-- 优先显示生产计划状态，如果没有则显示流程状态 -->
          <dict-tag
            v-if="row.productionPlan?.status !== undefined"
            :type="DICT_TYPE.WORK_ORDER_STATUS"
            :value="row.productionPlan.status"
            size="small"
            class="process-tag"
          />
          <el-tag
            v-else
            :type="getProcessStatusType(getProcessStatus(row, 'plan'))"
            size="small"
            class="process-tag"
            :class="getProcessStatusClass(getProcessStatus(row, 'plan'))"
            effect="plain"
          >
            <Icon :icon="getProcessStatusIcon(getProcessStatus(row, 'plan'))" class="tag-icon" />
            {{ getProcessStatusLabel(getProcessStatus(row, 'plan')) }}
          </el-tag>
              <div class="process-info">
                <div class="process-progress">{{ getProcessProgress(row, 'plan') }}%</div>
                <div class="process-divider"></div>
                <el-tooltip placement="top" v-if="row.productionPlan" effect="dark">
                  <template #content>
                    <div v-if="row.productionPlan">
                      <div>车间: {{ row.productionPlan.workshop || '-' }} | 产线: {{ row.productionPlan.productionLine || '-' }} | 负责人: {{ row.productionPlan.foreman || '-' }}</div>
                      <div v-if="row.productionPlan.schedule && Array.isArray(row.productionPlan.schedule) && row.productionPlan.schedule.length > 0">
                        <div style="margin: 4px 0 2px 0; font-weight: bold;">计划安排:</div>
                        <div v-for="(schedule, index) in row.productionPlan.schedule" :key="index" class="schedule-item">
                          <div class="schedule-row">
                            <span class="schedule-info">
                              第{{ index + 1 }}阶段: {{ formatDate(schedule.startTime) || '-' }} ~ {{ formatDate(schedule.endTime) || '-' }} | 计划: {{ schedule.quantity || '-' }}{{ row.salesOrderInfo?.product?.unitName || '' }} | 已完成: {{ schedule.fulfilledQty || 0 }}{{ row.salesOrderInfo?.product?.unitName || '' }}
                            </span>
                            <span v-if="schedule.status !== undefined" class="schedule-status">
                              | 状态: <dict-tag :type="DICT_TYPE.WORK_ORDER_STATUS" :value="schedule.status" />
                            </span>
                          </div>
                        </div>
                      </div>
                      <div class="tooltip-summary-section">
                        <div class="tooltip-summary-item">时间: {{ formatDate(getProcessStartDate(row, 'plan')) }} ~ {{ formatDate(getProcessEndDate(row, 'plan')) }}</div>
                        <div class="tooltip-summary-item">计划：{{ getPlanQuantity(row) }} | 已完成：{{ row.productionPlan?.fulfilledQty || 0 }} {{ row.salesOrderInfo?.product?.unitName || '' }}</div>
                      </div>
                    </div>
                  </template>
                  <div class="stock-summary">
                    <Icon icon="ep:view" class="view-icon" />
                  </div>
                </el-tooltip>
              </div>
            </div>
        </template>
      </el-table-column>

        <!-- 生产 -->
      <el-table-column
          label="生产"
        align="center"
        width="150"
      >
        <template #default="{ row }">
            <div class="process-cell">
          <!-- 优先显示生产执行状态，如果没有则显示流程状态 -->
          <dict-tag
            v-if="row.productionExecution?.status !== undefined"
            :type="DICT_TYPE.COMMON_TASK_STATUS"
            :value="row.productionExecution.status"
            size="small"
            class="process-tag"
          />
          <el-tag
            v-else
            :type="getProcessStatusType(getProcessStatus(row, 'production'))"
            size="small"
            class="process-tag"
            :class="getProcessStatusClass(getProcessStatus(row, 'production'))"
            effect="plain"
          >
            <Icon :icon="getProcessStatusIcon(getProcessStatus(row, 'production'))" class="tag-icon" />
            {{ getProcessStatusLabel(getProcessStatus(row, 'production')) }}
          </el-tag>
            <div class="process-info">
              <div class="stock-progress">
                <div class="progress-bar">
                  <div
                    class="progress-fill"
                    :class="getProcessProgress(row, 'production') >= 80 ? 'progress-green' : getProcessProgress(row, 'production') >= 60 ? 'progress-yellow' : 'progress-red'"
                    :style="{ width: Math.min(getProcessProgress(row, 'production'), 100) + '%' }"></div>
                </div>
                <span class="progress-text">{{ getProcessProgress(row, 'production') }}%</span>
              </div>
              <div class="process-divider"></div>
                <el-tooltip placement="top" v-if="row.productionExecution?.reportRecords && row.productionExecution.reportRecords.length > 0" effect="dark">
                  <template #content>
                    <div v-if="row.productionExecution">
                      <div style="margin: 4px 0 2px 0; font-weight: bold;">报工记录:</div>
                      <div v-for="(record, index) in row.productionExecution.reportRecords" :key="index" class="schedule-item">
                        <div class="report-record-row">
                          <span class="report-info">
                            第{{ index + 1 }}次: {{ formatDate(record.reportTime) || '-' }} | 报工人: {{ record.worker || '-' }} | 数量: {{ record.reportedQty || 0 }}{{ row.salesOrderInfo?.product?.unitName || '' }} | 产线: {{ record.productionLine || '-' }}
                          </span>
                          <span v-if="record.temperature || record.humidity" class="report-env">
                            | 温度: {{ record.temperature || '-' }}°C | 湿度: {{ record.humidity || '-' }}%
                          </span>
                          <span v-if="record.memo" class="report-memo">
                            | 备注: {{ record.memo }}
                          </span>
                        </div>
                      </div>
                      <div class="tooltip-summary-section">
                        <div class="tooltip-summary-item">最新时间: {{ formatDate(getProcessLatestDate(row, 'production')) }}</div>
                        <div class="tooltip-summary-item">报工：{{ getProductionQuantity(row) }} | 已生产：{{ row.productionExecution?.quantity || 0 }} {{ row.salesOrderInfo?.product?.unitName || '' }}</div>
                      </div>
                    </div>
                  </template>
                  <div class="stock-summary">
                    <Icon icon="ep:view" class="view-icon" />
                  </div>
                </el-tooltip>
              </div>
            </div>
        </template>
      </el-table-column>

        <!-- 质检 -->
      <el-table-column
          label="质检"
        align="center"
        width="140"
      >
        <template #default="{ row }">
            <div class="process-cell">
          <!-- 优先显示质检状态，如果没有则显示流程状态 -->
          <dict-tag
            v-if="row.qualityInspection?.status !== undefined"
            :type="DICT_TYPE.COMMON_TASK_STATUS"
            :value="row.qualityInspection.status"
            size="small"
            class="process-tag"
          />
          <el-tag
            v-else
            :type="getProcessStatusType(getProcessStatus(row, 'quality'))"
            size="small"
            class="process-tag"
            :class="getProcessStatusClass(getProcessStatus(row, 'quality'))"
            effect="plain"
          >
            <Icon :icon="getProcessStatusIcon(getProcessStatus(row, 'quality'))" class="tag-icon" />
            {{ getProcessStatusLabel(getProcessStatus(row, 'quality')) }}
          </el-tag>
              <div class="process-info">
                <el-tooltip placement="top" v-if="row.qualityInspection?.inspectionRecords && row.qualityInspection.inspectionRecords.length > 0" effect="dark">
                  <template #content>
                    <div v-if="row.qualityInspection">
                      <div style="margin: 4px 0 2px 0; font-weight: bold;">质检记录:</div>
                      <div v-for="(record, index) in row.qualityInspection.inspectionRecords" :key="index" class="schedule-item">
                        <div class="inspection-record-row">
                          <span class="inspection-info">
                            第{{ index + 1 }}次: {{ formatDate(record.inspectTime) || '-' }} | 质检人: {{ record.inspector || '-' }} | 数量: {{ record.inspectedQty || 0 }}{{ row.salesOrderInfo?.product?.unitName || '' }} | 结果:
                          </span>
                          <dict-tag :type="DICT_TYPE.INSPECT_RESULT" :value="record.result" class="inspection-result" />
                          <span v-if="record.memo" class="inspection-memo">
                            | 备注: {{ record.memo }}
                          </span>
                        </div>
                      </div>
                      <div style="margin-top: 8px; padding-top: 8px; border-top: 1px solid rgba(255,255,255,0.2);">
                        <div class="process-detail">质检员: {{ getQualityInspector(row) }}</div>
                        <div class="process-detail">合格：{{ getQualityQuantity(row) }}</div>
                        <div class="process-detail">已质检：{{ getQualityInspectedQuantity(row) }}</div>
                      </div>
                    </div>
                  </template>
                  <div class="stock-summary">
                    <Icon icon="ep:view" class="view-icon" />
                  </div>
                </el-tooltip>
                <div v-else>
                  <div class="process-detail">{{ getQualityInspector(row) }}</div>
                  <div class="process-detail">合格：{{ getQualityQuantity(row) }}</div>
                  <div class="process-detail">已质检：{{ getQualityInspectedQuantity(row) }}</div>
                </div>
              </div>
            </div>
        </template>
      </el-table-column>

        <!-- 入库 -->
      <el-table-column
          label="入库"
        align="center"
        width="140"
          class-name="hidden-on-tablet"
      >
        <template #default="{ row }">
            <div class="process-cell">
          <!-- 优先显示入库状态，如果没有则显示流程状态 -->
          <dict-tag
            v-if="row.warehousing?.status !== undefined"
            :type="DICT_TYPE.COMMON_TASK_STATUS"
            :value="row.warehousing.status"
            size="small"
            class="process-tag"
          />
          <el-tag
            v-else
            :type="getProcessStatusType(getProcessStatus(row, 'warehouseIn'))"
            size="small"
            class="process-tag"
            :class="getProcessStatusClass(getProcessStatus(row, 'warehouseIn'))"
            effect="plain"
          >
            <Icon :icon="getProcessStatusIcon(getProcessStatus(row, 'warehouseIn'))" class="tag-icon" />
            {{ getProcessStatusLabel(getProcessStatus(row, 'warehouseIn')) }}
          </el-tag>
              <div class="process-info">
                <el-tooltip placement="top" v-if="row.warehousing?.warehouseRecords && row.warehousing.warehouseRecords.length > 0" effect="dark">
                  <template #content>
                    <div v-if="row.warehousing">
                      <div style="margin: 4px 0 2px 0; font-weight: bold;">入库记录:</div>
                      <div v-for="(record, index) in row.warehousing.warehouseRecords" :key="index" class="schedule-item">
                        <div class="warehouse-record-row">
                          <span class="warehouse-info">
                            第{{ index + 1 }}次: {{ formatDate(record.warehouseTime) || '-' }} | 仓库: {{ record.warehouseName || '-' }} | 操作人: {{ record.operator || '-' }} | 数量: {{ record.inboundQty || 0 }}{{ row.salesOrderInfo?.product?.unitName || '' }} | 审核: {{ record.approveStatus || '-' }}
                          </span>
                          <span v-if="record.memo" class="warehouse-memo">
                            | 备注: {{ record.memo }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </template>
                  <div class="stock-summary">
                    <Icon icon="ep:view" class="view-icon" />
                  </div>
                </el-tooltip>
                <div v-else class="process-detail">{{ formatDate(getProcessDate(row, 'warehouseIn')) }}</div>
                <div class="process-detail">入库：{{ getWarehouseInQuantity(row) }}</div>
                <div class="process-detail">已入库：{{ row.warehousing?.quantity || 0 }} {{ row.salesOrderInfo?.product?.unitName || '' }}</div>
              </div>
            </div>
        </template>
      </el-table-column>

        <!-- 发货 -->
      <el-table-column
          label="发货"
        align="center"
        width="140"
          class-name="hidden-on-mobile"
      >
        <template #default="{ row }">
            <div class="process-cell">
          <!-- 优先显示发货状态，如果没有则显示流程状态 -->
          <dict-tag
            v-if="row.delivery?.status !== undefined"
            :type="DICT_TYPE.COMMON_TASK_STATUS"
            :value="row.delivery.status"
            size="small"
            class="process-tag"
          />
          <el-tag
            v-else
            :type="getProcessStatusType(getProcessStatus(row, 'delivery'))"
            size="small"
            class="process-tag"
            :class="getProcessStatusClass(getProcessStatus(row, 'delivery'))"
            effect="plain"
          >
            <Icon :icon="getProcessStatusIcon(getProcessStatus(row, 'delivery'))" class="tag-icon" />
            {{ getProcessStatusLabel(getProcessStatus(row, 'delivery')) }}
          </el-tag>
              <div class="process-info">
                <el-tooltip placement="top" v-if="row.delivery?.deliveryRecords && row.delivery.deliveryRecords.length > 0" effect="dark">
                  <template #content>
                    <div v-if="row.delivery" class="delivery-tooltip-content">
                      <div class="tooltip-header">发货记录</div>
                      <div v-for="(record, index) in row.delivery.deliveryRecords" :key="index" class="delivery-record-item">
                        <div class="record-header">
                          <span class="record-index">第{{ index + 1 }}次发货</span>
                          <span class="record-time">{{ formatDate(record.deliveryTime) || '-' }}</span>
                          <span class="record-quantity">{{ record.quantity || 0 }}{{ row.salesOrderInfo?.product?.unitName || '' }}</span>
                        </div>
                        <div v-if="record.logistics" class="logistics-details">
                          <div class="logistics-row">
                            <span class="label">客户信息:</span>
                            <span class="value">{{ record.logistics.customerName || '-' }}</span>
                            <span class="separator">|</span>
                            <span class="label">联系人:</span>
                            <span class="value">{{ record.logistics.customerContact || '-' }}</span>
                            <span class="separator">|</span>
                            <span class="label">电话:</span>
                            <span class="value">{{ record.logistics.customerPhone || '-' }}</span>
                          </div>
                          <div class="logistics-row">
                            <span class="label">收货地址:</span>
                            <span class="value address">{{ record.logistics.customerAddress || '-' }}</span>
                          </div>
                          <div class="logistics-row">
                            <span class="label">物流公司:</span>
                            <span class="value">{{ record.logistics.logisticsCompany || '-' }}</span>
                            <span class="separator">|</span>
                            <span class="label">运输方式:</span>
                            <span class="value">{{ record.logistics.transportMethod || '-' }}</span>
                          </div>
                          <div class="logistics-row" v-if="record.logistics.logisticsTrackingNumber">
                            <span class="label">物流单号:</span>
                            <span class="value tracking-number">{{ record.logistics.logisticsTrackingNumber }}</span>
                          </div>
                        </div>
                        <div v-if="record.memo" class="record-memo">
                          <span class="label">备注:</span>
                          <span class="value">{{ record.memo }}</span>
                        </div>
                      </div>
                    </div>
                  </template>
                  <div class="stock-summary">
                    <Icon icon="ep:view" class="view-icon" />
                  </div>
                </el-tooltip>
                <div v-else class="process-detail">{{ formatDate(getProcessDate(row, 'delivery')) }}</div>
                <div class="process-detail">已发货：{{ getDeliveryQuantity(row) }}</div>
                <div class="process-detail">进度：{{ getProcessProgress(row, 'delivery') }}%</div>
              </div>
          </div>
        </template>
      </el-table-column>

        <!-- 出库 -->
      <el-table-column
          label="出库"
        align="center"
        width="140"
      >
        <template #default="{ row }">
            <div class="process-cell">
          <!-- 优先显示出库状态，如果没有则显示流程状态 -->
          <dict-tag
            v-if="row.outbound?.status !== undefined"
            :type="DICT_TYPE.COMMON_TASK_STATUS"
            :value="row.outbound.status"
            size="small"
            class="process-tag"
          />
          <el-tag
            v-else
            :type="getProcessStatusType(getProcessStatus(row, 'warehouseOut'))"
            size="small"
            class="process-tag"
            :class="getProcessStatusClass(getProcessStatus(row, 'warehouseOut'))"
            effect="plain"
          >
            <Icon :icon="getProcessStatusIcon(getProcessStatus(row, 'warehouseOut'))" class="tag-icon" />
            {{ getProcessStatusLabel(getProcessStatus(row, 'warehouseOut')) }}
          </el-tag>
              <div class="process-info">
                <el-tooltip placement="top" v-if="row.outbound?.outboundRecords && row.outbound.outboundRecords.length > 0" effect="dark">
                  <template #content>
                    <div v-if="row.outbound" class="outbound-tooltip-content">
                      <div class="tooltip-header">出库记录</div>
                      <div v-for="(record, index) in row.outbound.outboundRecords" :key="index" class="outbound-record-item">
                        <div class="record-header">
                          <span class="record-index">第{{ index + 1 }}次出库</span>
                          <span class="record-time">{{ formatDate(record.outboundTime) || '-' }}</span>
                          <span class="record-quantity">{{ record.outboundQty || 0 }}{{ row.salesOrderInfo?.product?.unitName || '' }}</span>
                        </div>
                        <div class="outbound-details">
                          <div class="outbound-row">
                            <span class="label">操作人:</span>
                            <span class="value">{{ record.operator || '-' }}</span>
                            <span class="separator" v-if="record.outboundNo">|</span>
                            <span class="label" v-if="record.outboundNo">出库编号:</span>
                            <span class="value outbound-no" v-if="record.outboundNo">{{ record.outboundNo }}</span>
                          </div>
                        </div>
                        <div v-if="record.memo" class="record-memo">
                          <span class="label">备注:</span>
                          <span class="value">{{ record.memo }}</span>
                        </div>
                      </div>
                    </div>
                  </template>
                  <div class="stock-summary">
                    <Icon icon="ep:view" class="view-icon" />
                  </div>
                </el-tooltip>
                <div v-else class="process-detail">{{ formatDate(getProcessLatestDate(row, 'warehouseOut')) }}</div>
                <div class="process-detail">已出库：{{ getWarehouseOutQuantity(row) }}</div>
                <div class="process-detail">进度：{{ getProcessProgress(row, 'warehouseOut') }}%</div>
              </div>
            </div>
        </template>
      </el-table-column>

        <!-- 操作 -->
<!--      <el-table-column
        label="操作"
        min-width="120"
        fixed="right"
        align="center"
      >
        <template #default="{ row }">
          <el-button
            link
            type="primary"
            size="small"
            @click="handleOrderDetail(row)"
          >
            <Icon icon="ep:view" />
            详情
          </el-button>
          <el-button
            v-if="canEdit(row)"
            link
            type="warning"
            size="small"
            @click="handleEdit(row)"
          >
            <Icon icon="ep:edit" />
            编辑
          </el-button>
        </template>
      </el-table-column>-->
    </el-table>
<!--    </div>-->

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="currentTotal"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        class="custom-pagination"
      />
    </div>

    <!-- 批量操作栏 -->
    <div v-if="selectedRows.length > 0" class="batch-actions">
      <div class="batch-content">
        <span class="batch-info">
          已选择 {{ selectedRows.length }} 条记录
        </span>
        <div class="batch-buttons">
          <el-button size="small" @click="handleBatchExport" class="batch-btn">
            批量导出
          </el-button>
          <el-button size="small" @click="handleBatchPrint" class="batch-btn">
            批量打印
          </el-button>
          <el-button size="small" type="danger" @click="handleBatchCancel" class="batch-btn batch-btn-danger">
            批量取消
          </el-button>
        </div>
      </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Icon } from '@/components/Icon'
import { formatDate as formatDateUtil } from '@/utils/formatTime'
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import type { UnifiedOrderVO } from '../types'
import { OrderProcessApi } from '@/api/scm/sale/orderProcess'
import { formatToDate } from '@/utils/dateUtil'

interface OrderTotals {
  saleTotal: number
  purchaseTotal: number
  workTotal: number
  total: number
}

interface Props {
  queryParams: any
  loading?: boolean
  tableData?: UnifiedOrderVO[]
  orderTotals?: OrderTotals
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  tableData: () => [],
  orderTotals: () => ({ saleTotal: 0, purchaseTotal: 0, workTotal: 0, total: 0 })
})

const emit = defineEmits<{
  'update:query-params': [value: any]
  'order-detail': [order: UnifiedOrderVO]
  'page-change': [params: { pageNo: number, pageSize: number, orderType: string }]
  'order-type-change': [orderType: string]
}>()

// 路由
const router = useRouter()

// 响应式数据
const selectedRows = ref<UnifiedOrderVO[]>([])
const sortField = ref('')
const sortOrder = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const queryFormRef = ref()

// 搜索相关的本地查询参数
const defaultParams = {
  pageNo: 1,
  pageSize: 100,
  orderNo: '',
  customerName: '',
  approvalStatus: '',
  orderStatus: '',
  paymentStatus: '',
  deliveryStatus: '',
  invoiceStatus: '',
  timeRange: '',
  customerType: '',
  productType: ''
}

const localQueryParams = reactive({
  ...defaultParams,
  ...props.queryParams
})

// 监听 props 变化，同步到本地变量
watch(() => props.queryParams, (newParams) => {
  Object.assign(localQueryParams, newParams)
}, { deep: true, immediate: true })

// 计算属性 - 直接显示传入的销售订单数据
const displayData = computed(() => {
  const data = props.tableData || []
  // 只显示销售订单
  return data.filter(item => item.orderType === 'sale' || !item.orderType)
})

// 计算当前总数（只统计销售订单）
const currentTotal = computed(() => {
  return props.orderTotals?.saleTotal || displayData.value.length || 0
})

// 表格样式函数
const getRowClassName = ({ row }: { row: any, rowIndex: number }) => {
  if (row.isException) {
    return 'exception-row'
  }
  return ''
}

const getHeaderCellStyle = () => {
  return {
    backgroundColor: '#ffffff',
    color: '#374151',
    fontWeight: '600',
    fontSize: '13px',
    padding: '12px 8px',
    borderBottom: '2px solid #e5e7eb'
  }
}

const getCellStyle = () => {
  return {
    padding: '12px 8px',
    fontSize: '13px'
  }
}

// 工具函数
const formatDate = (date: any) => {
  if (!date) return '-'
  return formatDateUtil(date, 'YYYY-MM-DD')
}



// 获取产品信息的工具函数
const getFirstOrderDetail = (order: any) => order.orderDetails?.[0] || {}

// 通用数量格式化函数
const formatQuantityWithUnit = (quantity: number, unit: string) => {
  return quantity ? `${quantity}${unit}` : '-'
}

// 获取库存进度
const getStockProgress = (order: any) => {
  return order.materialInventory?.progress || 0
}

// 获取库存进度颜色
const getStockProgressColor = (order: any) => {
  const progress = getStockProgress(order)
  return progress >= 80 ? 'progress-green' : progress >= 60 ? 'progress-yellow' : 'progress-red'
}



// 流程状态映射
const PROCESS_STATUS = {
  PENDING: 'pending',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  FAILED: 'failed'
} as const

// 获取流程状态
const getProcessStatus = (order: any, processType: string) => {
  const progress = order.progress || 0
  const approvalStatus = order.approvalStatus || '0'

  const statusMap = {
    plan: (() => {
      // 根据生产计划状态判断
      if (order.productionPlan?.status !== undefined) {
        const planStatus = order.productionPlan.status
        // 根据字典 work_order_status: 0-待开始, 1-进行中, 2-已暂停, 3-已完成, 4-已取消
        if (planStatus === 3) return PROCESS_STATUS.COMPLETED
        if (planStatus === 1 || planStatus === 2) return PROCESS_STATUS.IN_PROGRESS
        if (planStatus === 4) return PROCESS_STATUS.FAILED
        return PROCESS_STATUS.PENDING
      }
      // 兜底逻辑：根据审批状态判断
      return approvalStatus === '1' ? PROCESS_STATUS.COMPLETED : PROCESS_STATUS.PENDING
    })(),
    purchase: progress > 20 ? PROCESS_STATUS.COMPLETED : progress > 0 ? PROCESS_STATUS.IN_PROGRESS : PROCESS_STATUS.PENDING,
    production: (() => {
      // 根据生产执行状态判断
      if (order.productionExecution?.status !== undefined) {
        const execStatus = order.productionExecution.status
        // 根据字典 common_task_status: 0-待开始, 1-进行中, 2-已暂停, 3-已完成, 4-已取消
        if (execStatus === 3) return PROCESS_STATUS.COMPLETED
        if (execStatus === 1 || execStatus === 2) return PROCESS_STATUS.IN_PROGRESS
        if (execStatus === 4) return PROCESS_STATUS.FAILED
        return PROCESS_STATUS.PENDING
      }
      // 兜底逻辑：根据进度判断
      return progress > 60 ? PROCESS_STATUS.COMPLETED : progress > 30 ? PROCESS_STATUS.IN_PROGRESS : PROCESS_STATUS.PENDING
    })(),
    quality: (() => {
      // 根据质检状态判断
      if (order.qualityInspection?.status !== undefined) {
        const qualityStatus = order.qualityInspection.status
        // 根据字典 common_task_status: 0-待开始, 1-进行中, 2-已暂停, 3-已完成, 4-已取消
        if (qualityStatus === 3) return PROCESS_STATUS.COMPLETED
        if (qualityStatus === 1 || qualityStatus === 2) return PROCESS_STATUS.IN_PROGRESS
        if (qualityStatus === 4) return PROCESS_STATUS.FAILED
        return PROCESS_STATUS.PENDING
      }
      // 兜底逻辑：根据进度判断
      return progress > 80 ? PROCESS_STATUS.COMPLETED : progress > 50 ? PROCESS_STATUS.IN_PROGRESS : PROCESS_STATUS.PENDING
    })(),
    warehouseIn: (() => {
      // 根据入库状态判断
      if (order.warehousing?.status !== undefined) {
        const warehouseStatus = order.warehousing.status
        // 根据字典 common_task_status: 0-待开始, 1-进行中, 2-已暂停, 3-已完成, 4-已取消
        if (warehouseStatus === 3) return PROCESS_STATUS.COMPLETED
        if (warehouseStatus === 1 || warehouseStatus === 2) return PROCESS_STATUS.IN_PROGRESS
        if (warehouseStatus === 4) return PROCESS_STATUS.FAILED
        return PROCESS_STATUS.PENDING
      }
      // 兜底逻辑：根据进度判断
      return progress > 85 ? PROCESS_STATUS.COMPLETED : progress > 60 ? PROCESS_STATUS.IN_PROGRESS : PROCESS_STATUS.PENDING
    })(),
    delivery: (() => {
      // 根据发货状态判断
      if (order.delivery?.status !== undefined) {
        const deliveryStatus = order.delivery.status
        // 根据字典 common_task_status: 0-待开始, 1-进行中, 2-已暂停, 3-已完成, 4-已取消
        if (deliveryStatus === 3) return PROCESS_STATUS.COMPLETED
        if (deliveryStatus === 1 || deliveryStatus === 2) return PROCESS_STATUS.IN_PROGRESS
        if (deliveryStatus === 4) return PROCESS_STATUS.FAILED
        return PROCESS_STATUS.PENDING
      }
      // 兜底逻辑：根据原有逻辑判断
      return order.deliveryStatus === '1' ? PROCESS_STATUS.COMPLETED : progress > 70 ? PROCESS_STATUS.IN_PROGRESS : PROCESS_STATUS.PENDING
    })(),
    warehouseOut: (() => {
      // 根据出库状态判断
      if (order.outbound?.status !== undefined) {
        const outboundStatus = order.outbound.status
        // 根据字典 common_task_status: 0-待开始, 1-进行中, 2-已暂停, 3-已完成, 4-已取消
        if (outboundStatus === 3) return PROCESS_STATUS.COMPLETED
        if (outboundStatus === 1 || outboundStatus === 2) return PROCESS_STATUS.IN_PROGRESS
        if (outboundStatus === 4) return PROCESS_STATUS.FAILED
        return PROCESS_STATUS.PENDING
      }
      // 兜底逻辑：根据原有逻辑判断
      return progress >= 100 ? PROCESS_STATUS.COMPLETED : progress > 80 ? PROCESS_STATUS.IN_PROGRESS : PROCESS_STATUS.PENDING
    })()
  }

  return statusMap[processType] || PROCESS_STATUS.PENDING
}

// 获取流程进度
const getProcessProgress = (order: any, processType: string) => {
  const progress = order.progress || 0
  switch (processType) {
    case 'plan':
      // 优先使用生产计划的进度
      if (order.productionPlan?.progress !== undefined) {
        return Math.round(order.productionPlan.progress * 100) / 100 // 保留两位小数
      }
      // 兜底逻辑
      return progress > 0 ? 100 : 0
    case 'purchase':
      return Math.min(progress * 0.3, 100)
    case 'production':
      // 优先使用生产执行的进度计算
      if (order.productionExecution?.quantity && order.salesOrderInfo?.product?.quantity) {
        const productionProgress = (order.productionExecution.quantity / order.salesOrderInfo.product.quantity) * 100
        return Math.round(productionProgress * 100) / 100 // 保留两位小数
      }
      // 兜底逻辑
      return Math.min(progress * 0.8, 100)
    case 'quality':
      // 优先使用质检记录的进度计算
      if (order.qualityInspection?.inspectionRecords && Array.isArray(order.qualityInspection.inspectionRecords) && order.salesOrderInfo?.product?.quantity) {
        const totalInspected = order.qualityInspection.inspectionRecords.reduce((sum, record) => {
          return sum + (record.inspectedQty || 0)
        }, 0)
        const qualityProgress = (totalInspected / order.salesOrderInfo.product.quantity) * 100
        return Math.round(qualityProgress * 100) / 100 // 保留两位小数
      }
      // 兜底逻辑
      return Math.min(progress * 0.9, 100)
    case 'warehouseIn':
      // 优先使用入库数量的进度计算
      if (order.warehousing?.quantity && order.salesOrderInfo?.product?.quantity) {
        const warehouseProgress = (order.warehousing.quantity / order.salesOrderInfo.product.quantity) * 100
        return Math.round(warehouseProgress * 100) / 100 // 保留两位小数
      }
      // 兜底逻辑
      return Math.min(progress * 0.95, 100)
    case 'delivery':
      // 优先使用发货数量的进度计算
      if (order.delivery?.quantity && order.salesOrderInfo?.product?.quantity) {
        const deliveryProgress = (order.delivery.quantity / order.salesOrderInfo.product.quantity) * 100
        return Math.round(deliveryProgress * 100) / 100 // 保留两位小数
      }
      // 如果有发货记录，计算发货记录的总进度
      if (order.delivery?.deliveryRecords && Array.isArray(order.delivery.deliveryRecords) && order.salesOrderInfo?.product?.quantity) {
        const totalDelivered = order.delivery.deliveryRecords.reduce((sum, record) => {
          return sum + (record.quantity || 0)
        }, 0)
        const deliveryProgress = (totalDelivered / order.salesOrderInfo.product.quantity) * 100
        return Math.round(deliveryProgress * 100) / 100 // 保留两位小数
      }
      // 兜底逻辑
      return Math.min(progress * 0.98, 100)
    case 'warehouseOut':
      // 优先使用出库数量的进度计算
      if (order.outbound?.quantity && order.salesOrderInfo?.product?.quantity) {
        const outboundProgress = (order.outbound.quantity / order.salesOrderInfo.product.quantity) * 100
        return Math.round(outboundProgress * 100) / 100 // 保留两位小数
      }
      // 如果有出库记录，计算出库记录的总进度
      if (order.outbound?.outboundRecords && Array.isArray(order.outbound.outboundRecords) && order.salesOrderInfo?.product?.quantity) {
        const totalOutbound = order.outbound.outboundRecords.reduce((sum, record) => {
          return sum + (record.outboundQty || 0)
        }, 0)
        const outboundProgress = (totalOutbound / order.salesOrderInfo.product.quantity) * 100
        return Math.round(outboundProgress * 100) / 100 // 保留两位小数
      }
      // 兜底逻辑
      return progress
    default:
      return 0
  }
}

// 流程状态配置
const PROCESS_STATUS_CONFIG = {
  [PROCESS_STATUS.COMPLETED]: {
    type: 'success' as const,
    class: 'status-completed',
    icon: 'ep:check',
    label: '完成'
  },
  [PROCESS_STATUS.IN_PROGRESS]: {
    type: 'primary' as const,
    class: 'status-in-progress',
    icon: 'ep:loading',
    label: '进行中'
  },
  [PROCESS_STATUS.PENDING]: {
    type: 'warning' as const,
    class: 'status-pending',
    icon: 'ep:clock',
    label: '未开始'
  },
  [PROCESS_STATUS.FAILED]: {
    type: 'danger' as const,
    class: 'status-failed',
    icon: 'ep:close',
    label: '失败'
  }
} as const

// 获取流程状态配置
const getProcessStatusConfig = (status: string) => {
  return PROCESS_STATUS_CONFIG[status] || PROCESS_STATUS_CONFIG[PROCESS_STATUS.PENDING]
}

// 获取流程状态类型
const getProcessStatusType = (status: string) => getProcessStatusConfig(status).type

// 获取流程状态样式类
const getProcessStatusClass = (status: string) => getProcessStatusConfig(status).class

// 获取流程状态图标
const getProcessStatusIcon = (status: string) => getProcessStatusConfig(status).icon

// 获取流程状态标签
const getProcessStatusLabel = (status: string) => getProcessStatusConfig(status).label

// 获取流程开始日期
const getProcessStartDate = (order: any, processType: string) => {
  if (processType === 'plan' && order.productionPlan?.schedule && Array.isArray(order.productionPlan.schedule) && order.productionPlan.schedule.length > 0) {
    // 取第一个计划的开始时间，或者最早的开始时间
    const schedules = order.productionPlan.schedule.filter(s => s.startTime)
    if (schedules.length > 0) {
      return schedules.sort((a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime())[0].startTime
    }
  }
  return order.orderDate || new Date()
}

// 获取流程结束日期
const getProcessEndDate = (order: any, processType: string) => {
  if (processType === 'plan' && order.productionPlan?.schedule && Array.isArray(order.productionPlan.schedule) && order.productionPlan.schedule.length > 0) {
    // 取最后一个计划的结束时间，或者最晚的结束时间
    const schedules = order.productionPlan.schedule.filter(s => s.endTime)
    if (schedules.length > 0) {
      return schedules.sort((a, b) => new Date(b.endTime).getTime() - new Date(a.endTime).getTime())[0].endTime
    }
  }
  return order.estimatedDeliveryDate || order.deliveryDate || new Date()
}

// 获取流程完成日期
const getProcessCompleteDate = (order: any, _processType: string) => {
  return order.completedDate || order.estimatedDeliveryDate
}

// 获取流程最新日期
const getProcessLatestDate = (order: any, _processType: string) => {
  return order.completedDate || order.estimatedDeliveryDate
}

// 获取流程日期
const getProcessDate = (order: any, processType: string) => {
  if (processType === 'warehouseIn') {
    return order.completedDate || order.estimatedDeliveryDate
  }
  if (processType === 'delivery') {
    return order.deliveryDate || order.estimatedDeliveryDate
  }
  return order.completedDate || order.estimatedDeliveryDate
}



// 格式化日期范围
const formatDateRange = (startDate: any, endDate: any) => {
  if (!startDate || !endDate) return '-'
  const start = formatDate(startDate).substring(5) // 去掉年份
  const end = formatDate(endDate).substring(5)
  return `${start}至${end}`
}

// 获取计划数量（优先使用生产计划数量）
const getPlanQuantity = (order: any) => {
  // 优先使用生产计划的计划数量
  if (order.productionPlan?.plannedQty !== undefined) {
    const unit = order.salesOrderInfo?.product?.unitName || ''
    return `${order.productionPlan.plannedQty}${unit}`
  }

  // 兜底逻辑：使用订单明细数量
  const detail = getFirstOrderDetail(order)
  const quantity = detail.quantity || order.quantity || 0
  const unit = detail.specUnit || detail.unit || order.unit || order.unitName || ''
  return `${quantity}${unit}`
}



// 获取生产数量（报工总数量）
const getProductionQuantity = (order: any) => {
  const unit = order.salesOrderInfo?.product?.unitName || order.unit || order.unitName || ''

  // 优先使用报工记录的总数量
  if (order.productionExecution?.reportRecords && Array.isArray(order.productionExecution.reportRecords)) {
    const totalReported = order.productionExecution.reportRecords.reduce((sum, record) => {
      return sum + (record.reportedQty || 0)
    }, 0)
    return formatQuantityWithUnit(totalReported, unit)
  }

  // 兜底逻辑
  return formatQuantityWithUnit(order.productionQuantity || 0, unit)
}

// 获取质检合格数量
const getQualityQuantity = (order: any) => {
  const unit = order.salesOrderInfo?.product?.unitName || order.unit || order.unitName || ''

  // 优先使用质检记录中合格的数量
  if (order.qualityInspection?.inspectionRecords && Array.isArray(order.qualityInspection.inspectionRecords)) {
    const qualifiedQty = order.qualityInspection.inspectionRecords
      .filter(record => record.result === 'qualified' || record.result === '1') // 假设合格结果为 'qualified' 或 '1'
      .reduce((sum, record) => sum + (record.inspectedQty || 0), 0)
    return formatQuantityWithUnit(qualifiedQty, unit)
  }

  // 兜底逻辑
  return formatQuantityWithUnit(order.qualityQuantity || 0, unit)
}

// 获取已质检数量
const getQualityInspectedQuantity = (order: any) => {
  const unit = order.salesOrderInfo?.product?.unitName || order.unit || order.unitName || ''

  // 优先使用质检记录的总数量
  if (order.qualityInspection?.inspectionRecords && Array.isArray(order.qualityInspection.inspectionRecords)) {
    const totalInspected = order.qualityInspection.inspectionRecords.reduce((sum, record) => {
      return sum + (record.inspectedQty || 0)
    }, 0)
    return formatQuantityWithUnit(totalInspected, unit)
  }

  // 兜底逻辑
  return formatQuantityWithUnit(0, unit)
}

// 获取质检员
const getQualityInspector = (order: any) => {
  // 优先使用最新的质检记录中的质检员
  if (order.qualityInspection?.inspectionRecords && Array.isArray(order.qualityInspection.inspectionRecords) && order.qualityInspection.inspectionRecords.length > 0) {
    const latestRecord = order.qualityInspection.inspectionRecords[order.qualityInspection.inspectionRecords.length - 1]
    return latestRecord.inspector || '未指定'
  }

  // 兜底逻辑
  return order.qualityInspector || order.inspector || '张工'
}

// 获取入库数量（入库记录总数量）
const getWarehouseInQuantity = (order: any) => {
  const unit = order.salesOrderInfo?.product?.unitName || order.unit || order.unitName || ''

  // 优先使用入库记录的总数量
  if (order.warehousing?.warehouseRecords && Array.isArray(order.warehousing.warehouseRecords)) {
    const totalInbound = order.warehousing.warehouseRecords.reduce((sum, record) => {
      return sum + (record.inboundQty || 0)
    }, 0)
    return formatQuantityWithUnit(totalInbound, unit)
  }

  // 兜底逻辑
  return formatQuantityWithUnit(order.inboundQuantity || 0, unit)
}



// 获取发货数量（发货记录总数量）
const getDeliveryQuantity = (order: any) => {
  const unit = order.salesOrderInfo?.product?.unitName || order.unit || order.unitName || ''

  // 优先使用发货记录的总数量
  if (order.delivery?.deliveryRecords && Array.isArray(order.delivery.deliveryRecords)) {
    const totalDelivered = order.delivery.deliveryRecords.reduce((sum, record) => {
      return sum + (record.quantity || 0)
    }, 0)
    return formatQuantityWithUnit(totalDelivered, unit)
  }

  // 兜底逻辑：使用发货数量字段
  return formatQuantityWithUnit(order.delivery?.quantity || 0, unit)
}

// 获取发货通知数量
const getDeliveryNoticeQuantity = (order: any) => {
  const unit = order.unit || order.unitName || ''
  return formatQuantityWithUnit(order.noticeQuantity, unit)
}

// 获取出库数量（出库记录总数量）
const getWarehouseOutQuantity = (order: any) => {
  const unit = order.salesOrderInfo?.product?.unitName || order.unit || order.unitName || ''

  // 优先使用出库记录的总数量
  if (order.outbound?.outboundRecords && Array.isArray(order.outbound.outboundRecords)) {
    const totalOutbound = order.outbound.outboundRecords.reduce((sum, record) => {
      return sum + (record.outboundQty || 0)
    }, 0)
    return formatQuantityWithUnit(totalOutbound, unit)
  }

  // 兜底逻辑：使用出库数量字段
  return formatQuantityWithUnit(order.outbound?.quantity || order.outboundQuantity || 0, unit)
}

// 搜索相关方法
const handleParamsChange = () => {
  // 直接传递所有参数，让父组件处理
  emit('update:query-params', { ...localQueryParams })
}

const handleQuery = () => {
  emit('update:query-params', { ...localQueryParams })
  // 触发父组件重新加载数据
  emit('page-change', {
    pageNo: 1,
    pageSize: pageSize.value,
    orderType: 'sale'
  })
}

const resetQuery = () => {
  queryFormRef.value?.resetFields()
  Object.assign(localQueryParams, defaultParams)
  emit('update:query-params', { ...localQueryParams })
  // 触发父组件重新加载数据
  emit('page-change', {
    pageNo: 1,
    pageSize: pageSize.value,
    orderType: 'sale'
  })
}

// 事件处理方法
const handleSortChange = ({ prop, order }: any) => {
  sortField.value = prop
  sortOrder.value = order
  // 触发父组件重新加载数据
  emit('page-change', {
    pageNo: currentPage.value,
    pageSize: pageSize.value,
    orderType: 'sale'
  })
}

const handleSelectionChange = (selection: UnifiedOrderVO[]) => {
  selectedRows.value = selection
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  console.log('页面大小变化:', size, '重置到第1页')
  emit('page-change', {
    pageNo: currentPage.value,
    pageSize: pageSize.value,
    orderType: 'sale'
  })
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  console.log('页码变化:', page, '页面大小:', pageSize.value)
  emit('page-change', {
    pageNo: currentPage.value,
    pageSize: pageSize.value,
    orderType: 'sale'
  })
}

const handleOrderDetail = (order: UnifiedOrderVO) => {
  // 检查订单ID
  const orderId = order.id || order.orderId
  if (!orderId) {
    ElMessage.warning('订单ID不存在，无法跳转')
    return
  }

  const orderNo = order.orderNo

  // 跳转到订单履约详情页面
  router.push({
    name: 'SaleOrderProcessDetail',
    params: { id: orderId.toString() },
    query: {
      title: orderNo || '订单履约详情'
    }
  })
}



const refreshData = () => {
  // 触发父组件重新加载数据
  emit('page-change', {
    pageNo: currentPage.value,
    pageSize: pageSize.value,
    orderType: 'sale'
  })
  ElMessage.success('数据已刷新')
}

// 处理下拉菜单命令
const handleDropdownCommand = (command: string) => {
  switch (command) {
    case 'export':
      exportData()
      break
    case 'init':
      initToMongo()
      break
    default:
      break
  }
}

const exportData = async () => {
  try {
    // 显示加载状态
    const loading = ElMessage({
      message: '正在导出数据...',
      type: 'info',
      duration: 0
    })

    // 模拟导出过程
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 创建CSV内容
    const csvContent = generateCSV()

    // 下载文件
    downloadCSV(csvContent, '销售订单履约数据.csv')

    loading.close()
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请稍后重试')
  }
}

// 生成CSV内容
const generateCSV = () => {
  const headers = ['订单编号', '客户名称', '产品名称', '数量', '审批状态', '创建时间']
  const rows = props.tableData.map(order => [
    order.orderNo || order.salesOrderInfo?.orderNo || '',
    order.salesOrderInfo?.customer?.name || '',
    order.salesOrderInfo?.product?.productName || '',
    order.salesOrderInfo?.product?.quantity || 0,
    order.salesOrderInfo?.approvalStatus || '',
    formatToDate(order.salesOrderInfo?.orderDate) || ''
  ])

  const csvContent = [headers, ...rows]
    .map(row => row.map(field => `"${field}"`).join(','))
    .join('\n')

  return csvContent
}

// 下载CSV文件
const downloadCSV = (content: string, filename: string) => {
  const blob = new Blob(['\uFEFF' + content], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', filename)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 批量操作
const handleBatchExport = () => {
  ElMessage.info(`批量导出 ${selectedRows.value.length} 条记录`)
}

const handleBatchPrint = () => {
  ElMessage.info(`批量打印 ${selectedRows.value.length} 条记录`)
}

const handleBatchCancel = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要取消选中的 ${selectedRows.value.length} 个订单吗？`,
      '批量取消确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    ElMessage.success('批量取消成功')
    selectedRows.value = []
    // 触发父组件重新加载数据
    emit('page-change', {
      pageNo: currentPage.value,
      pageSize: pageSize.value,
      orderType: 'sale'
    })
  } catch {
    // 用户取消操作
  }
}



// 数据加载


const initToMongo = async () => {
  try {
    await ElMessageBox.confirm(
      '此操作将重新初始化数据到MongoDB，是否继续？',
      '确认初始化',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const loading = ElMessage({
      message: '正在初始化数据...',
      type: 'info',
      duration: 0
    })

    await OrderProcessApi.initToMongo()

    loading.close()
    ElMessage.success('初始化成功')

    // 初始化完成后刷新数据
    refreshData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('初始化失败:', error)
      ElMessage.error('初始化失败，请稍后重试')
    }
  }
}

// 组件挂载时触发初始分页事件（仅在没有数据时）
onMounted(() => {
  // 如果没有数据，触发分页事件确保父组件加载数据
  if (!props.tableData || props.tableData.length === 0) {
    emit('page-change', {
      pageNo: currentPage.value,
      pageSize: pageSize.value,
      orderType: 'sale'
    })
  }
})
</script>

<style scoped>
/* 表格头部样式 */
.table-header {
  background: #ffffff;
  border-bottom: 2px solid #e5e7eb;
  padding: 20px 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: nowrap; /* 防止换行，确保搜索表单在最右侧 */
  gap: 20px;
}

.header-title {
  flex: 0 0 auto;
  min-width: 200px;
}

.title-text {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 4px 0;
}

.title-subtitle {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
  line-height: 1.2;
}

.header-search {
  flex: 0 0 auto; /* 不允许伸缩，保持固定宽度 */
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  margin-left: auto; /* 确保搜索表单推到最右侧 */
}

/* 搜索表单容器 - 单行布局 */
.search-form-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%; /* 使用100%宽度，不设置固定最小宽度 */
  max-width: 100%; /* 确保不超出父容器 */
  flex: 1; /* 占据可用空间 */
}

/* 单行布局：输入框和按钮在同一行 */
.search-single-row {
  display: flex;
  align-items: center;
  gap: 6px; /* 减小间距以节省空间 */
  flex-wrap: wrap; /* 允许换行以防止溢出 */
  width: 100%;
  justify-content: flex-start; /* 左对齐 */
}

/* 保留原有样式以兼容其他可能的使用 */
.search-inputs-row {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: nowrap;
}

.search-buttons-row {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: flex-end; /* 按钮靠右对齐 */
}

.search-input {
  width: 120px; /* 减小输入框宽度 */
  min-width: 100px;
  flex-shrink: 1;
}

.search-select {
  width: 100px; /* 减小下拉框宽度 */
  min-width: 90px;
  flex-shrink: 1;
}

/* 使用普通下拉框样式，移除特殊样式 */

.search-btn {
  border-radius: 20px;
  padding: 4px 12px; /* 大幅减小内边距 */
  height: 28px; /* 减小高度 */
  font-weight: 500;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  border: none;
  transition: all 0.2s ease;
  font-size: 11px; /* 减小字体 */
  flex-shrink: 0;
}

.search-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.reset-btn {
  border-radius: 20px;
  padding: 4px 10px; /* 大幅减小内边距 */
  height: 28px; /* 减小高度 */
  font-weight: 500;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  color: #64748b;
  transition: all 0.2s ease;
  font-size: 11px; /* 减小字体 */
  flex-shrink: 0;
}

.reset-btn:hover {
  background-color: #f1f5f9;
  border-color: #cbd5e1;
  color: #475569;
}

/* 统一的表单按钮样式 */
.form-btn {
  border-radius: 6px; /* 使用标准圆角 */
  padding: 6px 12px; /* 减小内边距 */
  height: 32px; /* 标准高度 */
  font-size: 13px; /* 减小字体大小 */
  font-weight: 500;
  flex-shrink: 0;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  white-space: nowrap; /* 防止文字换行 */
}

.form-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 更多操作下拉菜单样式 */
.more-actions-dropdown {
  margin-left: 8px;
}

.more-actions-dropdown .form-btn {
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  color: #666;
}

.more-actions-dropdown .form-btn:hover {
  background-color: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

/* 下拉菜单项图标样式 */
.dropdown-icon {
  margin-right: 6px;
  font-size: 14px;
}

/* 重置按钮特殊样式 */
.reset-btn-styled {
  background-color: #f8fafc !important;
  border-color: #e2e8f0 !important;
  color: #64748b !important;
}

.reset-btn-styled:hover {
  background-color: #f1f5f9 !important;
  border-color: #cbd5e1 !important;
  color: #475569 !important;
}

/* 按钮图标样式 */
.btn-icon {
  margin-right: 4px;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .header-title {
    text-align: left; /* 保持左对齐 */
  }

  .header-search {
    justify-content: flex-end; /* 保持右对齐 */
    margin-left: 0; /* 重置margin */
  }

  .search-form-container {
    width: 100%; /* 在中等屏幕上使用100%宽度 */
    max-width: 100%;
  }

  .search-single-row {
    gap: 4px; /* 进一步减小间距 */
  }

  .search-input {
    width: 110px; /* 在中等屏幕上进一步减小宽度 */
    min-width: 90px;
  }

  .search-select {
    width: 90px;
    min-width: 80px;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-wrap: wrap; /* 允许换行 */
  }

  .header-search {
    justify-content: center; /* 在小屏幕上居中 */
    width: 100%;
  }

  .search-form-container {
    width: 100%;
    max-width: 100%;
  }

  .search-single-row {
    flex-wrap: wrap; /* 允许输入框和按钮换行 */
    justify-content: flex-start; /* 左对齐，更紧凑 */
    gap: 4px; /* 进一步减小间距 */
  }

  .search-inputs-row {
    flex-wrap: wrap; /* 允许输入框换行 */
    justify-content: center;
  }

  .search-input,
  .search-select {
    width: 100px; /* 在小屏幕上使用更小的宽度 */
    min-width: 90px;
  }

  .form-btn {
    padding: 6px 8px; /* 在小屏幕上减小按钮内边距 */
    font-size: 12px; /* 减小字体 */
  }

  .search-buttons-row {
    justify-content: flex-end; /* 在小屏幕上也保持右对齐 */
  }

  /* 在小屏幕上调整下拉菜单 */
  .more-actions-dropdown {
    margin-left: 4px;
  }

  .search-btn,
  .reset-btn {
    flex: 1;
    max-width: 120px;
  }
}

/* header-actions 样式已移除，按钮已合并到搜索表单中 */

.header-actions :deep(.el-button:hover) {
  transform: translateY(-1px);
}

.action-btn {
  height: 34px;
  padding: 0 16px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.export-btn {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  border-color: rgba(59, 130, 246, 0.2);
}

.export-btn:hover {
  background: rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
}

.refresh-btn {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
  border-color: rgba(107, 114, 128, 0.2);
}

.refresh-btn:hover {
  background: rgba(107, 114, 128, 0.15);
  border-color: rgba(107, 114, 128, 0.3);
  transform: translateY(-1px);
}

.btn-icon {
  margin-right: 6px;
  font-size: 14px;
}

/* 表格容器样式 */
.table-container {
  padding: 24px 24px 0 24px;
  background: #ffffff;
}

.custom-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  width: 100%;
}

/* 表格行样式 */
:deep(.el-table__row) {
  transition: background-color 0.2s ease;
}

:deep(.el-table__row:hover) {
  background-color: #f8fafc;
}

:deep(.exception-row) {
  background-color: #fef2f2;
}

:deep(.exception-row:hover) {
  background-color: #fee2e2;
}

/* 单元格样式 */
.order-cell {
  padding: 4px 0;
  overflow: hidden;
}

.order-no {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.order-link {
  font-size: 0.875rem;
  font-weight: 600;
  text-decoration: none;
  transition: color 0.2s ease;
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.order-link:hover {
  color: #2563eb;
}

.customer-name {
  font-size: 0.75rem;
  color: #64748b;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-cell {
  padding: 4px 0;
  overflow: hidden;
}

.product-name {
  font-weight: 500;
  color: #1e293b;
  margin-bottom: 4px;
  font-size: 0.875rem;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-code {
  font-size: 0.75rem;
  color: #64748b;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.quantity-cell {
  padding: 4px 0;
  text-align: center;
}

.main-quantity {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
  font-size: 0.875rem;
}

.completed-quantity {
  font-size: 0.75rem;
  color: #10b981;
  font-weight: 500;
}

.stock-cell {
  padding: 4px 0;
  text-align: center;
}

.stock-status-tag {
  margin-bottom: 8px;
}

.stock-progress {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6px;
}

.progress-bar {
  width: 50px;
  height: 6px;
  background-color: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
  margin-right: 8px;
}

.progress-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-green {
  background-color: #10b981;
}

.progress-yellow {
  background-color: #f59e0b;
}

.progress-red {
  background-color: #ef4444;
}

.progress-text {
  font-size: 0.75rem;
  font-weight: 600;
  color: #374151;
}

.stock-info {
  font-size: 0.75rem;
  color: #64748b;
}

/* 流程单元格样式 */
.process-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 8px 4px;
  min-height: 85px;
  position: relative;
}

.process-tag {
  margin-bottom: 8px;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 6px;
  padding: 4px 8px;
  border: 1px solid transparent;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  min-width: 70px;
  line-height: 1;
  height: 24px; /* 固定标签高度 */
  flex-shrink: 0; /* 防止标签被压缩 */
}

.process-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tag-icon {
  font-size: 12px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* 自定义标签样式覆盖 */
:deep(.process-tag.el-tag) {
  font-weight: 500;
  font-size: 0.75rem;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  min-width: 70px;
  line-height: 1;
  border: 1px solid transparent;
  height: 24px; /* 固定标签高度 */
  flex-shrink: 0; /* 防止标签被压缩 */
  box-sizing: border-box; /* 确保padding包含在高度内 */
}

/* 让内部内容同样使用flex，确保图标与文字垂直居中 */
:deep(.process-tag.el-tag .el-tag__content) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  height: 100%; /* 确保内容填满标签高度 */
  line-height: 1; /* 统一行高 */
}

/* 根据状态类恢复颜色（参考HTML表现） */
:deep(.status-completed.el-tag) {
  background-color: rgba(16, 185, 129, 0.1); /* success/10 */
  color: #10b981; /* success main */
}

:deep(.status-in-progress.el-tag) {
  background-color: rgba(37, 99, 235, 0.1); /* primary/10 */
  color: #2563eb; /* primary main */
}

:deep(.status-pending.el-tag) {
  background-color: #e5e7eb; /* gray-200 */
  color: #6b7280; /* gray-500 */
}

:deep(.status-failed.el-tag) {
  background-color: rgba(239, 68, 68, 0.1); /* danger/10 */
  color: #ef4444; /* danger main */
}

:deep(.status-default.el-tag) {
  background-color: rgba(59, 130, 246, 0.05); /* light blue */
  color: #4b5563; /* gray-600 */
}

/* dict-tag 组件样式统一 */
:deep(.process-cell .dict-tag),
:deep(.process-cell .dict-tag.process-tag) {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 24px !important; /* 与 process-tag 相同高度 */
  min-width: 70px !important;
  width: auto !important;
  padding: 0 !important; /* 移除外层padding，让内部el-tag处理 */
  border-radius: 6px !important;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
  line-height: 1 !important;
  box-sizing: border-box !important;
  margin-bottom: 8px !important;
  flex-shrink: 0 !important;
  transition: all 0.2s ease !important;
  overflow: hidden !important; /* 确保内部元素不会溢出 */
  gap: 0 !important; /* 移除内部间距 */
}

/* dict-tag 内部的 el-tag 样式 */
:deep(.process-cell .dict-tag .el-tag),
:deep(.process-cell .dict-tag.process-tag .el-tag) {
  height: 24px !important; /* 固定高度 */
  width: 100% !important;
  min-width: 70px !important; /* 最小宽度与容器一致 */
  max-width: none !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  border: none !important;
  margin: 0 !important;
  padding: 0 8px !important; /* 左右内边距 */
  font-size: 0.75rem !important;
  font-weight: 500 !important;
  line-height: 1 !important;
  border-radius: 6px !important;
  box-sizing: border-box !important;
  /* 强制背景色显示 */
  background-clip: padding-box !important;
  /* 移除默认的过渡效果可能导致的问题 */
  transition: none !important;
}

/* 确保所有标签容器在同一基线对齐 */
:deep(.process-cell > *:first-child) {
  align-self: center; /* 第一个子元素（标签）居中对齐 */
}

/* 统一标签基线对齐 */
.process-cell > .process-tag,
:deep(.process-cell > .dict-tag) {
  vertical-align: baseline;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* 强制 dict-tag 内部 el-tag 的背景色显示 */
:deep(.process-cell .dict-tag .el-tag.el-tag--success) {
  background-color: var(--el-color-success-light-9) !important;
  color: var(--el-color-success) !important;
  border-color: var(--el-color-success-light-8) !important;
}

:deep(.process-cell .dict-tag .el-tag.el-tag--warning) {
  background-color: var(--el-color-warning-light-9) !important;
  color: var(--el-color-warning) !important;
  border-color: var(--el-color-warning-light-8) !important;
}

:deep(.process-cell .dict-tag .el-tag.el-tag--danger) {
  background-color: var(--el-color-danger-light-9) !important;
  color: var(--el-color-danger) !important;
  border-color: var(--el-color-danger-light-8) !important;
}

:deep(.process-cell .dict-tag .el-tag.el-tag--info) {
  background-color: var(--el-color-info-light-9) !important;
  color: var(--el-color-info) !important;
  border-color: var(--el-color-info-light-8) !important;
}

:deep(.process-cell .dict-tag .el-tag.el-tag--primary) {
  background-color: var(--el-color-primary-light-9) !important;
  color: var(--el-color-primary) !important;
  border-color: var(--el-color-primary-light-8) !important;
}

/* 默认样式的 el-tag */
:deep(.process-cell .dict-tag .el-tag:not([class*="el-tag--"])) {
  background-color: var(--el-color-info-light-9) !important;
  color: var(--el-color-info) !important;
  border-color: var(--el-color-info-light-8) !important;
}

/* 自定义颜色的标签 */
:deep(.process-cell .dict-tag .el-tag[style*="color: #fff"]) {
  color: #fff !important;
}

.process-info {
  width: 100%;
  text-align: center;
  flex: 1; /* 让信息区域占据剩余空间 */
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.process-progress {
  font-size: 0.75rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 6px;
}

.process-divider {
  width: 70%;
  height: 1px;
  background-color: #e5e7eb;
  margin: 6px auto;
}

.process-detail {
  font-size: 0.7rem;
  color: #64748b;
  line-height: 1.2;
  margin-bottom: 2px;
}

/* Tooltip中的完成时间样式 */
.tooltip-completion-time {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  font-size: 0.75rem;
  color: #e5e7eb;
  line-height: 1;
  white-space: nowrap;
}

/* Tooltip中的汇总信息区域样式 */
.tooltip-summary-section {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.tooltip-summary-item {
  font-size: 0.75rem;
  color: #d1d5db;
  line-height: 1.2;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.approval-status {
  font-weight: 500;
}

.approval-success {
  color: #10b981;
}

.approval-danger {
  color: #ef4444;
}

.approval-pending {
  color: #f59e0b;
}

/* 操作单元格样式 */


.action-link {
  padding: 4px 8px;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 4px;
  transition: all 0.2s ease;
  margin: 0;
  min-width: auto;
}

.action-icon {
  margin-right: 4px;
  font-size: 12px;
}

/* 分页样式 */
.pagination-container {
  padding: 20px 24px;
  display: flex;
  justify-content: center;
  background: #ffffff;
  border-top: 1px solid #e2e8f0;
}

.custom-pagination {
  background: transparent;
}

/* 批量操作样式 */
.batch-actions {
  background: linear-gradient(135deg, #dbeafe 0%, #e0f2fe 100%);
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  margin: 16px 24px;
  padding: 12px 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.batch-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.batch-info {
  font-size: 0.875rem;
  color: #1e40af;
  font-weight: 500;
}

.batch-buttons {
  display: flex;
  gap: 8px;
}

.batch-btn {
  height: 32px;
  padding: 0 12px;
  font-size: 0.875rem;
  border-radius: 6px;
  font-weight: 500;
  border: 1px solid transparent;
  transition: all 0.2s ease;
}

.batch-btn-danger {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border-color: rgba(239, 68, 68, 0.2);
}

.batch-btn-danger:hover {
  background: rgba(239, 68, 68, 0.15);
  border-color: rgba(239, 68, 68, 0.3);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  :deep(.hidden-on-mobile) {
    display: none !important;
  }
}

@media (max-width: 992px) {
  .header-content {
    flex-direction: column;
  }

  .header-title,
  .header-search {
    width: 100%;
    min-width: unset;
  }

  /* header-actions 样式已移除 */
}

@media (max-width: 768px) {
  :deep(.hidden-on-tablet) {
    display: none !important;
  }
  
  /* header-actions 样式已移除 */
  
  .table-container {
    padding: 16px;
  }
  
  .pagination-container {
    padding: 16px;
  }
  
  .batch-actions {
    margin: 16px;
  }
  
  .batch-content {
    flex-direction: column;
    gap: 12px;
  }
}

@media (max-width: 640px) {
  .action-btn {
    padding: 0 12px;
    font-size: 0.8rem;
  }

  .btn-icon {
    margin-right: 4px;
  }
  
  .process-cell {
    padding: 6px 2px;
    min-height: 75px;
  }

  .process-tag {
    font-size: 0.7rem;
    padding: 1px 4px;
  }
  
  .process-detail {
    font-size: 0.65rem;
  }

  .header-search {
    margin-bottom: 10px;
  }
}

/* 表格组件深度样式 */
:deep(.el-table) {
  font-size: 13px;
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background: #f8fafc;
}

:deep(.el-table th) {
  background: #f8fafc;
  color: #374151;
  font-weight: 600;
  border-bottom: 2px solid #e2e8f0;
  padding: 12px 8px;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f1f5f9;
  padding: 12px 8px;
}

:deep(.el-table__body tr:last-child td) {
  border-bottom: none;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background: #fafbfc;
}

:deep(.el-table--border::after) {
  display: none;
}

:deep(.el-table--border, .el-table--group) {
  border: 1px solid #e2e8f0;
}

:deep(.el-table--border th, .el-table--border td) {
  border-right: 1px solid #f1f5f9;
}

:deep(.el-table--border th:last-child, .el-table--border td:last-child) {
  border-right: none;
}

/* 固定列样式优化 */
:deep(.el-table__fixed-right) {
  right: 0 !important;
}

:deep(.el-table__fixed-left) {
  left: 0 !important;
}

/* 操作列样式优化 */
:deep(.el-table__fixed-right .el-table__cell) {
  padding: 8px 12px;
}

:deep(.el-table__fixed-left .el-table__cell) {
  padding: 8px 12px;
}

/* 响应式标签样式 */
@media (max-width: 640px) {
  :deep(.process-tag.el-tag) {
    font-size: 0.65rem;
    padding: 2px 4px;
    min-width: 60px;
    height: 20px; /* 小屏幕上减小高度 */
  }

  /* 小屏幕上的 dict-tag 样式 */
  :deep(.process-cell .dict-tag),
  :deep(.process-cell .dict-tag.process-tag) {
    height: 20px !important;
    min-width: 60px !important;
    font-size: 0.65rem !important;
    padding: 0 !important; /* 外层容器无padding */
  }

  /* 小屏幕上的 dict-tag 内部 el-tag 样式 */
  :deep(.process-cell .dict-tag .el-tag),
  :deep(.process-cell .dict-tag.process-tag .el-tag) {
    padding: 0 4px !important; /* 内部标签的padding */
    height: 20px !important; /* 固定高度 */
    min-width: 60px !important;
    font-size: 0.65rem !important;
    border-radius: 4px !important; /* 小屏幕上使用更小的圆角 */
  }

  .process-cell {
    min-height: 75px;
    padding: 6px 2px;
  }
}

/* 分页样式 */
:deep(.el-pagination) {
  justify-content: center;
}

:deep(.el-pagination .el-pager li) {
  border-radius: 4px;
  margin: 0 2px;
}

:deep(.el-pagination .el-pager li.active) {
  background-color: #3b82f6;
  color: #ffffff;
}

:deep(.el-pagination .btn-next, .el-pagination .btn-prev) {
  border-radius: 4px;
}

:deep(.el-pagination .el-pagination__jump) {
  margin-left: 12px;
}

:deep(.el-pagination .el-pagination__sizes) {
  margin-right: 12px;
}

:deep(.el-pagination .el-pagination__total) {
  margin-right: 12px;
}

.view-icon {
  font-size: 16px;
  color: #409eff;
  vertical-align: middle;
}
.el-popper.is-customized {
  /* Set padding to ensure the height is 32px */
  padding: 6px 12px;
  background: linear-gradient(90deg, rgb(159, 229, 151), rgb(204, 229, 129));
}

.el-popper.is-customized .el-popper__arrow::before {
  background: linear-gradient(45deg, #b2e68d, #bce689);
  right: 0;
}
.header-search {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Tooltip中的dict-tag样式 */
:deep(.el-tooltip__popper .el-tag) {
  margin: 0 2px;
  font-size: 12px;
}

/* Tooltip中的计划安排样式 */
:deep(.el-tooltip__popper) {
  max-width: 350px;
}

:deep(.el-tooltip__popper .schedule-item) {
  margin: 2px 0;
  padding: 3px;
  border-left: 2px solid #409eff;
  background-color: rgba(64, 158, 255, 0.05);
}

/* stock-summary样式 */
.stock-summary {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 2px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.stock-summary:hover {
  background-color: rgba(64, 158, 255, 0.1);
}

/* 采购项目行样式 */
.purchase-item-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: 20px;
}

.purchase-info {
  flex: 1;
  margin-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.purchase-status {
  flex-shrink: 0;
}

/* 计划安排行样式 */
.schedule-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: 20px;
  font-weight: bold;
}

.schedule-info {
  flex: 1;
  margin-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.schedule-status {
  flex-shrink: 0;
}

/* 报工记录行样式 */
.report-record-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 2px 0;
  min-height: 20px;
  font-weight: bold;
}

.report-info {
  flex: 1;
  margin-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
}

.report-env,
.report-memo {
  flex-shrink: 0;
  white-space: nowrap;
}

/* 质检记录行样式 */
.inspection-record-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 2px 0;
  min-height: 20px;
  font-weight: bold;
}

.inspection-info {
  flex: 1;
  margin-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
}

.inspection-result {
  flex-shrink: 0;
  margin: 0 4px;
}

.inspection-memo {
  flex-shrink: 0;
  white-space: nowrap;
}

/* 入库记录行样式 */
.warehouse-record-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 2px 0;
  min-height: 20px;
  font-weight: bold;
}

.warehouse-info {
  flex: 1;
  margin-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
}

.warehouse-memo {
  flex-shrink: 0;
  white-space: nowrap;
}

/* 原料库存行样式 */
.material-item-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: 20px;
}

.material-info {
  flex: 1;
  margin-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.material-status {
  flex-shrink: 0;
}



/* 发货记录 Tooltip 样式 */
.delivery-tooltip-content {
  max-width: 500px;
  padding: 8px 0;
}

.tooltip-header {
  font-weight: bold;
  font-size: 14px;
  color: #ffffff;
  margin-bottom: 8px;
  padding-bottom: 4px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.delivery-record-item {
  margin-bottom: 12px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.delivery-record-item:last-child {
  margin-bottom: 0;
}

.record-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 6px;
  font-weight: 600;
}

.record-index {
  color: #60a5fa;
  font-size: 13px;
}

.record-time {
  color: #d1d5db;
  font-size: 12px;
}

.record-quantity {
  color: #34d399;
  font-size: 13px;
  font-weight: bold;
}

.logistics-details {
  margin: 6px 0;
}

.logistics-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 4px;
  font-size: 12px;
  line-height: 1.4;
}

.logistics-row:last-child {
  margin-bottom: 0;
}

.logistics-row .label {
  color: #9ca3af;
  font-weight: 500;
  margin-right: 4px;
  flex-shrink: 0;
}

.logistics-row .value {
  color: #ffffff;
  margin-right: 8px;
}

.logistics-row .value.address {
  max-width: 300px;
  word-break: break-all;
}

.logistics-row .value.tracking-number {
  color: #fbbf24;
  font-family: monospace;
  font-weight: 600;
}

.logistics-row .separator {
  color: #6b7280;
  margin: 0 6px;
}

.record-memo {
  margin-top: 6px;
  padding-top: 6px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 12px;
}

.record-memo .label {
  color: #9ca3af;
  font-weight: 500;
  margin-right: 4px;
}

.record-memo .value {
  color: #fde047;
  font-style: italic;
}

/* 出库记录 Tooltip 样式 */
.outbound-tooltip-content {
  max-width: 450px;
  padding: 8px 0;
}

.outbound-record-item {
  margin-bottom: 12px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.outbound-record-item:last-child {
  margin-bottom: 0;
}

.outbound-details {
  margin: 6px 0;
}

.outbound-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 4px;
  font-size: 12px;
  line-height: 1.4;
}

.outbound-row:last-child {
  margin-bottom: 0;
}

.outbound-row .label {
  color: #9ca3af;
  font-weight: 500;
  margin-right: 4px;
  flex-shrink: 0;
}

.outbound-row .value {
  color: #ffffff;
  margin-right: 8px;
}

.outbound-row .value.outbound-no {
  color: #fbbf24;
  font-family: monospace;
  font-weight: 600;
}

.outbound-row .separator {
  color: #6b7280;
  margin: 0 6px;
}
</style>
